"""
文件检测模块
负责检查指定目录下文件是否存在，支持日期时间戳文件检测
"""
import argparse
import json
import os
import re
import sys
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from core.logger import log
except ImportError:
    # 如果导入失败，使用简单的日志输出
    class SimpleLogger:
        def info(self, msg): print(f"[INFO] {msg}")
        def error(self, msg): print(f"[ERROR] {msg}")
        def warning(self, msg): print(f"[WARNING] {msg}")
        def debug(self, msg): print(f"[DEBUG] {msg}")
    
    log = SimpleLogger()


class FileDetector:
    """文件检测器类"""
    
    # 支持的图片格式
    IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'}
    
    # 日期时间戳模式
    DATETIME_PATTERNS = {
        'IMG_YYYYMMDD_HHMMSS_SSS': r'IMG_(\d{8})_(\d{6})_(\d{3})\.(jpg|jpeg|png|gif|bmp|webp)',
        'YYYYMMDD_HHMMSS': r'(\d{8})_(\d{6})\.(jpg|jpeg|png|gif|bmp|webp)',
        'YYYY-MM-DD_HH-MM-SS': r'(\d{4}-\d{2}-\d{2})_(\d{2}-\d{2}-\d{2})\.(jpg|jpeg|png|gif|bmp|webp)',
        'Screenshot_YYYYMMDD-HHMMSS': r'Screenshot_(\d{8})-(\d{6})\.(jpg|jpeg|png|gif|bmp|webp)'
    }
    
    def __init__(self, base_path: str = ""):
        """
        初始化文件检测器
        
        Args:
            base_path: 基础路径，如果为空则使用当前工作目录
        """
        self.base_path = base_path or os.getcwd()
        log.info(f"文件检测器初始化，基础路径: {self.base_path}")
    
    def check_file_exists(self, file_path: str) -> bool:
        """
        检查单个文件是否存在
        
        Args:
            file_path: 文件路径（相对或绝对路径）
            
        Returns:
            bool: 文件是否存在
        """
        try:
            # 如果是相对路径，则基于base_path构建完整路径
            if not os.path.isabs(file_path):
                full_path = os.path.join(self.base_path, file_path)
            else:
                full_path = file_path
            
            exists = os.path.isfile(full_path)
            log.debug(f"检查文件: {full_path} -> {'存在' if exists else '不存在'}")
            return exists
            
        except Exception as e:
            log.error(f"检查文件时发生错误: {e}")
            return False
    
    def scan_directory(self, directory: str, recursive: bool = False) -> List[Dict[str, Any]]:
        """
        扫描目录下的所有文件
        
        Args:
            directory: 目录路径
            recursive: 是否递归扫描子目录
            
        Returns:
            List[Dict]: 文件信息列表
        """
        try:
            # 构建完整目录路径
            if not os.path.isabs(directory):
                full_dir = os.path.join(self.base_path, directory)
            else:
                full_dir = directory
            
            if not os.path.exists(full_dir):
                log.warning(f"目录不存在: {full_dir}")
                return []
            
            files_info = []
            
            if recursive:
                # 递归扫描
                for root, dirs, files in os.walk(full_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        files_info.append(self._get_file_info(file_path))
            else:
                # 只扫描当前目录
                for item in os.listdir(full_dir):
                    item_path = os.path.join(full_dir, item)
                    if os.path.isfile(item_path):
                        files_info.append(self._get_file_info(item_path))
            
            log.info(f"扫描目录 {full_dir} 完成，找到 {len(files_info)} 个文件")
            return files_info
            
        except Exception as e:
            log.error(f"扫描目录时发生错误: {e}")
            return []
    
    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件详细信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 文件信息字典
        """
        try:
            stat = os.stat(file_path)
            file_info = {
                'path': file_path,
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'created_time': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'extension': os.path.splitext(file_path)[1].lower(),
                'is_image': os.path.splitext(file_path)[1].lower() in self.IMAGE_EXTENSIONS
            }
            return file_info
        except Exception as e:
            log.error(f"获取文件信息失败: {e}")
            return {'path': file_path, 'error': str(e)}
    
    def find_datetime_files(self, directory: str, pattern_name: str = 'IMG_YYYYMMDD_HHMMSS_SSS', 
                           target_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        查找符合日期时间戳模式的文件
        
        Args:
            directory: 搜索目录
            pattern_name: 模式名称
            target_date: 目标日期 (YYYYMMDD格式)，如果为None则使用当前日期
            
        Returns:
            List[Dict]: 匹配的文件信息列表
        """
        try:
            if pattern_name not in self.DATETIME_PATTERNS:
                log.error(f"不支持的模式: {pattern_name}")
                return []
            
            pattern = self.DATETIME_PATTERNS[pattern_name]
            target_date = target_date or datetime.now().strftime('%Y%m%d')
            
            # 扫描目录
            all_files = self.scan_directory(directory, recursive=False)
            matched_files = []
            
            for file_info in all_files:
                if 'error' in file_info:
                    continue
                
                filename = file_info['name']
                match = re.match(pattern, filename, re.IGNORECASE)
                
                if match:
                    # 提取日期信息
                    file_date = None
                    file_time = None
                    file_ms = None
                    file_ext = None

                    if pattern_name == 'IMG_YYYYMMDD_HHMMSS_SSS':
                        file_date = match.group(1)
                        file_time = match.group(2)
                        file_ms = match.group(3)
                        file_ext = match.group(4)
                    elif pattern_name == 'YYYYMMDD_HHMMSS':
                        file_date = match.group(1)
                        file_time = match.group(2)
                        file_ext = match.group(3)
                        file_ms = None
                    elif pattern_name == 'YYYY-MM-DD_HH-MM-SS':
                        file_date = match.group(1).replace('-', '')  # 转换为YYYYMMDD格式
                        file_time = match.group(2).replace('-', '')  # 转换为HHMMSS格式
                        file_ext = match.group(3)
                        file_ms = None
                    elif pattern_name == 'Screenshot_YYYYMMDD-HHMMSS':
                        file_date = match.group(1)
                        file_time = match.group(2)
                        file_ext = match.group(3)
                        file_ms = None

                    # 检查日期是否匹配
                    if file_date and file_date == target_date:
                        file_info.update({
                            'pattern_matched': pattern_name,
                            'extracted_date': file_date,
                            'extracted_time': file_time,
                            'extracted_ms': file_ms,
                            'file_extension': file_ext
                        })
                        matched_files.append(file_info)
            
            log.info(f"在目录 {directory} 中找到 {len(matched_files)} 个匹配日期 {target_date} 的文件")
            return matched_files
            
        except Exception as e:
            log.error(f"查找日期时间戳文件时发生错误: {e}")
            return []
    
    def check_camera_images(self, camera_dir: str = r"\DCIM\Camera", 
                           target_date: Optional[str] = None) -> Dict[str, Any]:
        """
        检查相机目录下的图片文件
        
        Args:
            camera_dir: 相机目录路径
            target_date: 目标日期 (YYYYMMDD格式)
            
        Returns:
            Dict: 检测结果
        """
        try:
            target_date = target_date or datetime.now().strftime('%Y%m%d')
            log.info(f"检查相机目录 {camera_dir} 中日期为 {target_date} 的图片")
            
            # 构建完整路径
            if not os.path.isabs(camera_dir):
                full_camera_dir = os.path.join(self.base_path, camera_dir.lstrip('\\').lstrip('/'))
            else:
                full_camera_dir = camera_dir
            
            result = {
                'directory': full_camera_dir,
                'target_date': target_date,
                'directory_exists': os.path.exists(full_camera_dir),
                'total_files': 0,
                'matched_files': [],
                'patterns_checked': list(self.DATETIME_PATTERNS.keys()),
                'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if not result['directory_exists']:
                log.warning(f"相机目录不存在: {full_camera_dir}")
                return result
            
            # 检查各种模式
            for pattern_name in self.DATETIME_PATTERNS.keys():
                matched = self.find_datetime_files(full_camera_dir, pattern_name, target_date)
                result['matched_files'].extend(matched)
            
            # 统计总文件数
            all_files = self.scan_directory(full_camera_dir, recursive=False)
            result['total_files'] = len(all_files)
            
            log.info(f"检查完成，共找到 {len(result['matched_files'])} 个匹配的图片文件")
            return result
            
        except Exception as e:
            log.error(f"检查相机图片时发生错误: {e}")
            return {'error': str(e)}
    
    def export_results(self, results: Dict[str, Any], output_file: str = None) -> str:
        """
        导出检测结果到JSON文件
        
        Args:
            results: 检测结果
            output_file: 输出文件路径
            
        Returns:
            str: 输出文件路径
        """
        try:
            if output_file is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = f"tools/output/file_detection_{timestamp}.json"
            
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            # 写入JSON文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            log.info(f"检测结果已导出到: {output_file}")
            return output_file
            
        except Exception as e:
            log.error(f"导出结果时发生错误: {e}")
            return ""


def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(description='文件检测工具')
    parser.add_argument('--directory', '-d', default=r'\DCIM\Camera',
                       help='要检查的目录路径 (默认: \\DCIM\\Camera)')
    parser.add_argument('--date', '-t', help='目标日期 (YYYYMMDD格式，默认为当前日期)')
    parser.add_argument('--base-path', '-b', help='基础路径')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--pattern', '-p', default='IMG_YYYYMMDD_HHMMSS_SSS',
                       choices=list(FileDetector.DATETIME_PATTERNS.keys()),
                       help='文件名模式')

    args = parser.parse_args()

    # 创建文件检测器
    detector = FileDetector(args.base_path)

    # 执行检测
    if args.directory.lower().endswith('camera') or 'dcim' in args.directory.lower():
        # 相机目录检测
        results = detector.check_camera_images(args.directory, args.date)
    else:
        # 普通目录检测 - 构建结果格式与相机目录检测一致
        matched_files = detector.find_datetime_files(args.directory, args.pattern, args.date)
        results = {
            'directory': args.directory,
            'target_date': args.date or datetime.now().strftime('%Y%m%d'),
            'directory_exists': os.path.exists(os.path.join(detector.base_path, args.directory) if not os.path.isabs(args.directory) else args.directory),
            'total_files': len(detector.scan_directory(args.directory, recursive=False)),
            'matched_files': matched_files,
            'patterns_checked': [args.pattern],
            'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    # 导出结果
    output_file = detector.export_results(results, args.output)

    # 打印摘要
    if isinstance(results, dict) and 'matched_files' in results:
        print(f"\n=== 检测摘要 ===")
        print(f"目录: {results.get('directory', args.directory)}")
        print(f"目标日期: {results.get('target_date', args.date or '当前日期')}")
        print(f"匹配文件数: {len(results['matched_files'])}")
        print(f"结果文件: {output_file}")

        if results['matched_files']:
            print(f"\n匹配的文件:")
            for file_info in results['matched_files'][:5]:  # 只显示前5个
                print(f"  - {file_info['name']}")
            if len(results['matched_files']) > 5:
                print(f"  ... 还有 {len(results['matched_files']) - 5} 个文件")
    else:
        print(f"\n=== 检测摘要 ===")
        print(f"目录: {args.directory}")
        print(f"目标日期: {args.date or '当前日期'}")
        print(f"匹配文件数: {len(results) if isinstance(results, list) else 0}")
        print(f"结果文件: {output_file}")


if __name__ == "__main__":
    main()
