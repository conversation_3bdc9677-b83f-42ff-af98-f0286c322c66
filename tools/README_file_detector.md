# 文件检测模块 (File Detector)

## 概述

文件检测模块是一个强大的文件检查工具，专门用于检测指定目录下的文件是否存在，特别擅长检测以日期时间戳命名的图片文件。

**🆕 新增功能**: 现已支持Android设备文件检测，可通过ADB直接检查Android手机上的文件！

## 主要功能

### 1. 基础文件检测
- 检查单个文件是否存在
- 支持相对路径和绝对路径
- 提供详细的文件信息
- **支持本地文件系统和Android设备**

### 2. 目录扫描
- 扫描指定目录下的所有文件
- 支持递归扫描子目录
- 获取文件的详细信息（大小、创建时间、修改时间等）
- **Android设备通过ADB命令实现**

### 3. 日期时间戳文件检测
- 支持多种日期时间戳命名模式
- 专门针对相机拍摄的图片文件
- 可指定目标日期进行精确匹配
- **完美支持Android相机和截图文件**

### 4. 图片文件过滤
- 自动识别常见图片格式
- 支持 jpg, jpeg, png, gif, bmp, webp, tiff 等格式

### 5. Android设备支持 🆕
- 通过ADB直接访问Android设备文件系统
- 自动检测常见目录（相机、截图、下载等）
- 支持多设备连接和设备选择
- 智能路径适配和错误处理

### 6. 结果导出
- 将检测结果导出为JSON格式
- 便于后续分析和处理
- 包含设备模式标识

## 支持的文件名模式

| 模式名称 | 正则表达式 | 示例文件名 |
|---------|-----------|-----------|
| `IMG_YYYYMMDD_HHMMSS_SSS` | `IMG_(\d{8})_(\d{6})_(\d{3})\.(jpg\|jpeg\|png\|gif\|bmp\|webp)` | `IMG_20250716_194815_970.jpg` |
| `YYYYMMDD_HHMMSS` | `(\d{8})_(\d{6})\.(jpg\|jpeg\|png\|gif\|bmp\|webp)` | `20250716_194815.jpg` |
| `YYYY-MM-DD_HH-MM-SS` | `(\d{4}-\d{2}-\d{2})_(\d{2}-\d{2}-\d{2})\.(jpg\|jpeg\|png\|gif\|bmp\|webp)` | `2025-07-16_19-48-15.jpg` |
| `Screenshot_YYYYMMDD-HHMMSS` | `Screenshot_(\d{8})-(\d{6})\.(jpg\|jpeg\|png\|gif\|bmp\|webp)` | `Screenshot_20250716-194815.png` |

## 使用方法

### 1. 命令行使用

#### 本地文件系统模式
```bash
# 检查默认相机目录下当前日期的图片
python tools/file_detector.py

# 检查指定目录和日期
python tools/file_detector.py -d "DCIM/Camera" -t "20250716"

# 指定基础路径和输出文件
python tools/file_detector.py -b "C:/Users/<USER>" -d "DCIM/Camera" -o "detection_results.json"

# 使用特定的文件名模式
python tools/file_detector.py -p "YYYYMMDD_HHMMSS" -d "Pictures"
```

#### Android设备模式 🆕
```bash
# 检查Android设备默认相机目录
python tools/file_detector.py --android

# 列出连接的Android设备
python tools/file_detector.py --list-devices

# 显示Android设备常见目录
python tools/file_detector.py --android --common-dirs

# 检查指定设备的相机目录
python tools/file_detector.py --android --device-id "YOUR_DEVICE_ID" -t "20250716"

# 检查Android截图目录
python tools/file_detector.py --android -d "/sdcard/Pictures/Screenshots" -p "Screenshot_YYYYMMDD-HHMMSS"

# 检查Android下载目录的特定文件
python tools/file_detector.py --android -d "/sdcard/Download" -t "20250716"
```

#### 命令行参数

| 参数 | 简写 | 说明 | 默认值 |
|------|------|------|--------|
| `--directory` | `-d` | 要检查的目录路径 | 本地:`\DCIM\Camera` Android:`/sdcard/DCIM/Camera` |
| `--date` | `-t` | 目标日期 (YYYYMMDD格式) | 当前日期 |
| `--base-path` | `-b` | 基础路径 (仅本地模式) | 当前工作目录 |
| `--output` | `-o` | 输出文件路径 | 自动生成 |
| `--pattern` | `-p` | 文件名模式 | `IMG_YYYYMMDD_HHMMSS_SSS` |
| `--android` | `-a` | 使用Android设备模式 | `False` |
| `--device-id` | `-i` | Android设备ID | 默认设备 |
| `--list-devices` | - | 列出连接的Android设备 | - |
| `--common-dirs` | - | 显示Android常见目录 | - |

### 2. 模块化使用

```python
from tools.file_detector import FileDetector

# 创建检测器实例
detector = FileDetector(base_path="/storage/emulated/0")

# 检查单个文件
exists = detector.check_file_exists("DCIM/Camera/IMG_20250716_194815_970.jpg")
print(f"文件存在: {exists}")

# 扫描目录
files = detector.scan_directory("DCIM/Camera")
print(f"找到 {len(files)} 个文件")

# 查找日期时间戳文件
matched_files = detector.find_datetime_files(
    directory="DCIM/Camera",
    pattern_name="IMG_YYYYMMDD_HHMMSS_SSS",
    target_date="20250716"
)

# 检查相机目录
results = detector.check_camera_images(
    camera_dir="DCIM/Camera",
    target_date="20250716"
)

# 导出结果
output_file = detector.export_results(results)
```

## 典型使用场景

### 场景1: 检查今天拍摄的照片

```python
from tools.file_detector import FileDetector
from datetime import datetime

detector = FileDetector()
today = datetime.now().strftime('%Y%m%d')

# 检查相机目录下今天的照片
results = detector.check_camera_images(
    camera_dir=r"\DCIM\Camera",
    target_date=today
)

print(f"今天拍摄了 {len(results['matched_files'])} 张照片")
```

### 场景2: 批量检查多个日期

```python
from tools.file_detector import FileDetector
from datetime import datetime, timedelta

detector = FileDetector()

# 检查最近7天的照片
for i in range(7):
    date = (datetime.now() - timedelta(days=i)).strftime('%Y%m%d')
    results = detector.check_camera_images(target_date=date)
    print(f"{date}: {len(results['matched_files'])} 张照片")
```

### 场景3: 自定义目录和模式

```python
from tools.file_detector import FileDetector

detector = FileDetector(base_path="/custom/path")

# 查找截图文件
screenshots = detector.find_datetime_files(
    directory="Screenshots",
    pattern_name="Screenshot_YYYYMMDD-HHMMSS",
    target_date="20250716"
)

print(f"找到 {len(screenshots)} 个截图文件")
```

## 输出格式

检测结果以JSON格式输出，包含以下信息：

```json
{
  "directory": "/DCIM/Camera",
  "target_date": "20250716",
  "directory_exists": true,
  "total_files": 150,
  "matched_files": [
    {
      "path": "/DCIM/Camera/IMG_20250716_194815_970.jpg",
      "name": "IMG_20250716_194815_970.jpg",
      "size": 2048576,
      "created_time": "2025-07-16 19:48:15",
      "modified_time": "2025-07-16 19:48:15",
      "extension": ".jpg",
      "is_image": true,
      "pattern_matched": "IMG_YYYYMMDD_HHMMSS_SSS",
      "extracted_date": "20250716",
      "extracted_time": "194815",
      "extracted_ms": "970",
      "file_extension": "jpg"
    }
  ],
  "patterns_checked": [
    "IMG_YYYYMMDD_HHMMSS_SSS",
    "YYYYMMDD_HHMMSS",
    "YYYY-MM-DD_HH-MM-SS",
    "Screenshot_YYYYMMDD-HHMMSS"
  ],
  "check_time": "2025-08-04 15:30:45"
}
```

## 运行示例

```bash
# 运行使用示例
python tools/examples/file_detector_examples.py
```

示例程序将演示：
1. 基础文件检查
2. 目录扫描
3. 日期时间戳模式检测
4. 相机目录检查
5. 自定义模式
6. 图片文件过滤

## 注意事项

1. **路径格式**: 支持Windows和Unix风格的路径分隔符
2. **权限要求**: 确保对目标目录有读取权限
3. **大目录处理**: 对于包含大量文件的目录，扫描可能需要一些时间
4. **日期格式**: 目标日期必须使用YYYYMMDD格式（如：20250716）
5. **模式匹配**: 文件名模式匹配不区分大小写

## 错误处理

模块包含完善的错误处理机制：
- 目录不存在时会给出警告
- 文件访问权限不足时会记录错误
- 无效的日期格式会被检测并报告
- 所有异常都会被捕获并记录到日志

## 扩展性

可以通过修改 `DATETIME_PATTERNS` 字典来添加新的文件名模式：

```python
# 添加自定义模式
FileDetector.DATETIME_PATTERNS['CUSTOM_PATTERN'] = r'custom_(\d{8})_(\d{6})\.(jpg|png)'
```
